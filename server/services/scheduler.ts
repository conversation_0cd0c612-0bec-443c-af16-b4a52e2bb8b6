import process from "node:process"
import { getters } from "#/getters"
import { getCacheTable } from "#/database/cache"
import { getHistoryTable } from "#/database/history"
import { DataSyncManager, SyncStateManager } from "#/utils/dataSync"

export class NewsScheduler {
  private timers: Map<string, NodeJS.Timeout> = new Map()
  private isRunning: boolean = false
  private defaultInterval: number
  private retentionDays: number
  private cleanupInterval: number

  constructor() {
    // 从环境变量读取配置，提供默认值
    this.defaultInterval = parseInt(process.env.SCHEDULER_INTERVAL || '600000') // 默认10分钟
    this.retentionDays = parseInt(process.env.DATA_RETENTION_DAYS || '30') // 默认保留30天
    this.cleanupInterval = parseInt(process.env.CLEANUP_INTERVAL || '86400000') // 默认24小时清理一次
    
    logger.info(`📅 Scheduler configured: interval=${this.defaultInterval}ms, retention=${this.retentionDays}days`)
  }

  async start() {
    if (this.isRunning) {
      logger.warn('📅 Scheduler is already running')
      return
    }

    this.isRunning = true
    logger.success('📅 Starting news scheduler...')

    // 启动所有新闻源的定时抓取
    await this.startAllSourceSchedulers()
    
    // 启动数据清理定时任务
    this.startCleanupScheduler()
    
    logger.success('📅 News scheduler started successfully')
  }

  async stop() {
    if (!this.isRunning) {
      logger.warn('📅 Scheduler is not running')
      return
    }

    this.isRunning = false
    
    // 停止所有定时器
    for (const [sourceId, timer] of this.timers) {
      clearInterval(timer)
      logger.info(`📅 Stopped scheduler for source: ${sourceId}`)
    }
    
    this.timers.clear()
    logger.success('📅 News scheduler stopped')
  }

  private async startAllSourceSchedulers() {
    const sourceIds = Object.keys(sources)
    logger.info(`📅 Starting schedulers for ${sourceIds.length} sources`)

    for (const sourceId of sourceIds) {
      await this.startSourceScheduler(sourceId)
    }
  }

  private async startSourceScheduler(sourceId: string) {
    if (this.timers.has(sourceId)) {
      clearInterval(this.timers.get(sourceId)!)
    }

    // 获取该源的刷新间隔，如果没有配置则使用默认值
    const sourceInterval = sources[sourceId]?.interval || this.defaultInterval
    
    // 立即执行一次
    await this.fetchAndSaveNews(sourceId)
    
    // 设置定时器
    const timer = setInterval(async () => {
      await this.fetchAndSaveNews(sourceId)
    }, sourceInterval)
    
    this.timers.set(sourceId, timer)
    logger.success(`📅 Started scheduler for source: ${sourceId} (interval: ${sourceInterval}ms)`)
  }

  private async fetchAndSaveNews(sourceId: string) {
    try {
      if (!getters[sourceId]) {
        logger.warn(`📅 No getter found for source: ${sourceId}`)
        return
      }

      // 检查是否最近有手动刷新（避免重复抓取）
      const cacheTable = await getCacheTable()
      const historyTable = await getHistoryTable()

      // 使用同步状态管理器检查冲突
      if (SyncStateManager.isInConflictWindow(sourceId)) {
        const lastFetch = SyncStateManager.getLastFetchTime(sourceId)
        const ageSeconds = Math.round((Date.now() - lastFetch) / 1000)
        DataSyncManager.logDataSync(sourceId, 'skip', { ageSeconds })
        return
      }

      logger.info(`📅 Fetching news for source: ${sourceId}`)
      const newsItems = await getters[sourceId]()
      
      if (!newsItems || newsItems.length === 0) {
        logger.warn(`📅 No news items fetched for source: ${sourceId}`)
        return
      }

      // 获取现有历史数据进行智能合并
      let finalData = newsItems.slice(0, 30)
      if (historyTable) {
        try {
          const existingHistory = await historyTable.getNewsBySource(sourceId, 30)
          finalData = DataSyncManager.mergeNewsData(newsItems, existingHistory, 30)

          if (existingHistory.length > 0) {
            DataSyncManager.logDataSync(sourceId, 'merge', {
              newCount: newsItems.length,
              historyCount: finalData.length - newsItems.length,
              totalCount: finalData.length
            })
          }
        } catch (error) {
          logger.warn(`📅 Error merging history for ${sourceId}:`, error)
        }
      }

      // 记录抓取时间
      SyncStateManager.recordFetchTime(sourceId)

      // 保存到历史记录（只保存新抓取的原始数据）
      if (historyTable) {
        await historyTable.saveNews(sourceId, newsItems)
      }

      // 更新缓存（使用合并后的数据）
      if (cacheTable) {
        await cacheTable.set(sourceId, finalData)
      }

      logger.success(`📅 Successfully fetched and saved ${newsItems.length} news items for source: ${sourceId} (${finalData.length} total after merge)`)
    } catch (error) {
      logger.error(`📅 Error fetching news for source ${sourceId}:`, error)
    }
  }

  private startCleanupScheduler() {
    // 立即执行一次清理
    this.performCleanup()
    
    // 设置定时清理
    const cleanupTimer = setInterval(() => {
      this.performCleanup()
    }, this.cleanupInterval)
    
    this.timers.set('__cleanup__', cleanupTimer)
    logger.success(`📅 Started cleanup scheduler (interval: ${this.cleanupInterval}ms, retention: ${this.retentionDays} days)`)
  }

  private async performCleanup() {
    try {
      logger.info('🧹 Starting data cleanup...')
      
      const historyTable = await getHistoryTable()
      if (historyTable) {
        const deletedCount = await historyTable.cleanupOldNews(this.retentionDays)
        logger.success(`🧹 Cleanup completed: removed ${deletedCount} old news items`)
      }
    } catch (error) {
      logger.error('🧹 Error during cleanup:', error)
    }
  }

  async addSource(sourceId: string) {
    if (!this.isRunning) {
      logger.warn('📅 Scheduler is not running, cannot add source')
      return
    }

    await this.startSourceScheduler(sourceId)
  }

  async removeSource(sourceId: string) {
    const timer = this.timers.get(sourceId)
    if (timer) {
      clearInterval(timer)
      this.timers.delete(sourceId)
      logger.success(`📅 Removed scheduler for source: ${sourceId}`)
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      activeSchedulers: Array.from(this.timers.keys()).filter(key => key !== '__cleanup__'),
      config: {
        defaultInterval: this.defaultInterval,
        retentionDays: this.retentionDays,
        cleanupInterval: this.cleanupInterval
      }
    }
  }

  async getSourceStats() {
    const historyTable = await getHistoryTable()
    if (historyTable) {
      return await historyTable.getSourceStats()
    }
    return []
  }
}

// 全局调度器实例
let globalScheduler: NewsScheduler | null = null

export function getScheduler(): NewsScheduler {
  if (!globalScheduler) {
    globalScheduler = new NewsScheduler()
  }
  return globalScheduler
}

// 在服务器启动时自动启动调度器
export async function initScheduler() {
  // 检查是否应该启动调度器
  const shouldStart = process.env.ENABLE_CACHE !== "false" && 
                     (process.env.NODE_ENV === "production" || process.env.DOCKER_ENV === "true")
  
  if (shouldStart) {
    const scheduler = getScheduler()
    // 延迟启动，确保数据库初始化完成
    setTimeout(async () => {
      try {
        await scheduler.start()
        logger.success('📅 Scheduler auto-started successfully')
      } catch (error) {
        logger.error('📅 Failed to auto-start scheduler:', error)
      }
    }, 5000) // 5秒延迟
  } else {
    logger.info('📅 Scheduler disabled (ENABLE_CACHE=false or development mode)')
  }
}
