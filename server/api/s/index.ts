import type { SourceID, SourceResponse } from "@shared/types"
import { getters } from "#/getters"
import { getCacheTable } from "#/database/cache"
import { getHistoryTable } from "#/database/history"
import { DataSyncManager, SyncStateManager } from "#/utils/dataSync"
import type { CacheInfo } from "#/types"

export default defineEventHandler(async (event): Promise<SourceResponse> => {
  try {
    const query = getQuery(event)
    const latest = query.latest !== undefined && query.latest !== "false"
    let id = query.id as SourceID
    const isValid = (id: SourceID) => !id || !sources[id] || !getters[id]

    if (isValid(id)) {
      const redirectID = sources?.[id]?.redirect
      if (redirectID) id = redirectID
      if (isValid(id)) throw new Error("Invalid source id")
    }

    const cacheTable = await getCacheTable()
    const historyTable = await getHistoryTable()
    // Date.now() in Cloudflare Worker will not update throughout the entire runtime.
    const now = Date.now()
    let cache: CacheInfo | undefined
    let historyItems: any[] = []
    let latestHistoryTime = 0

    // 总是尝试获取历史数据（用于后续合并和对比）
    if (historyTable) {
      try {
        historyItems = await historyTable.getNewsBySource(id, 30)
        if (historyItems.length > 0) {
          latestHistoryTime = Math.max(...historyItems.map(item => item.created_at))

          // 如果不是强制刷新，且历史数据足够新，直接返回历史数据
          if (!latest && now - latestHistoryTime < sources[id].interval) {
            DataSyncManager.logDataSync(id, 'history_hit', {
              ageSeconds: Math.round((now - latestHistoryTime) / 1000),
              itemCount: historyItems.length
            })

            return {
              status: "success",
              id,
              updatedTime: latestHistoryTime,
              items: historyItems.map(item => ({
                id: item.id,
                title: item.title,
                url: item.url,
                mobileUrl: item.mobileUrl,
                pubDate: item.pubDate,
                extra: item.extra
              })),
            }
          }
        }
      } catch (e) {
        logger.warn(`Failed to fetch history for ${id}:`, e)
      }
    }

    if (cacheTable) {
      cache = await cacheTable.get(id)
      if (cache) {
      // if (cache) {
        // interval 刷新间隔，对于缓存失效也要执行的。本质上表示本来内容更新就很慢，这个间隔内可能内容压根不会更新。
        // 默认 10 分钟，是低于 TTL 的，但部分 Source 的更新间隔会超过 TTL，甚至有的一天更新一次。
        if (now - cache.updated < sources[id].interval) {
          DataSyncManager.logDataSync(id, 'cache_hit', {
            ageSeconds: Math.round((now - cache.updated) / 1000),
            itemCount: cache.items.length
          })

          return {
            status: "success",
            id,
            updatedTime: now,
            items: cache.items,
          }
        }

        // 而 TTL 缓存失效时间，在时间范围内，就算内容更新了也要用这个缓存。
        // 复用缓存是不会更新时间的。
        if (now - cache.updated < TTL) {
          // 有 latest
          // 没有 latest，但服务器禁止登录

          // 没有 latest
          // 有 latest，服务器可以登录但没有登录
          if (!latest || (!event.context.disabledLogin && !event.context.user)) {
            return {
              status: "cache",
              id,
              updatedTime: cache.updated,
              items: cache.items,
            }
          }
        }
      }
    }

    try {
      const newData = (await getters[id]()).slice(0, 30)

      // 记录抓取时间（用于避免冲突）
      SyncStateManager.recordFetchTime(id, now)

      // 使用智能数据合并
      const finalData = DataSyncManager.mergeNewsData(newData, historyItems, 30)

      // 记录合并日志
      if (historyItems.length > 0 && newData.length > 0) {
        DataSyncManager.logDataSync(id, 'merge', {
          newCount: newData.length,
          historyCount: finalData.length - newData.length,
          totalCount: finalData.length
        })
      } else {
        DataSyncManager.logDataSync(id, 'fetch', {
          count: newData.length
        })
      }

      // 更新缓存
      if (cacheTable && finalData.length) {
        if (event.context.waitUntil) event.context.waitUntil(cacheTable.set(id, finalData))
        else await cacheTable.set(id, finalData)
      }

      // 保存到历史记录（只保存新抓取的数据，避免重复）
      if (historyTable && newData.length) {
        try {
          if (event.context.waitUntil) {
            event.context.waitUntil(historyTable.saveNews(id, newData))
          } else {
            await historyTable.saveNews(id, newData)
          }
        } catch (historyError) {
          logger.warn(`Failed to save history for ${id}:`, historyError)
        }
      }

      const logMessage = latest ? `fetch ${id} latest (forced refresh)` : `fetch ${id} latest`
      logger.success(logMessage)

      return {
        status: "success",
        id,
        updatedTime: now,
        items: finalData,
      }
    } catch (e) {
      if (cache!) {
        return {
          status: "cache",
          id,
          updatedTime: cache.updated,
          items: cache.items,
        }
      } else {
        throw e
      }
    }
  } catch (e: any) {
    logger.error(e)
    throw createError({
      statusCode: 500,
      message: e instanceof Error ? e.message : "Internal Server Error",
    })
  }
})
