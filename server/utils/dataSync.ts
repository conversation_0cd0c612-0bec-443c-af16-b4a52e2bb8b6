import type { NewsItem } from "@shared/types"
import type { HistoryItem } from "#/database/history"

/**
 * 智能数据合并工具
 * 用于合并新抓取的数据和历史数据，避免数据丢失
 */
export class DataSyncManager {
  /**
   * 合并新数据和历史数据
   * @param newData 新抓取的数据
   * @param historyData 历史数据
   * @param maxItems 最大返回项目数
   * @returns 合并后的数据
   */
  static mergeNewsData(
    newData: NewsItem[], 
    historyData: HistoryItem[], 
    maxItems: number = 30
  ): NewsItem[] {
    if (!newData.length && !historyData.length) {
      return []
    }

    if (!newData.length) {
      return historyData.slice(0, maxItems).map(item => ({
        id: item.id,
        title: item.title,
        url: item.url,
        mobileUrl: item.mobileUrl,
        pubDate: item.pubDate,
        extra: item.extra
      }))
    }

    if (!historyData.length) {
      return newData.slice(0, maxItems)
    }

    // 创建ID映射用于快速查找
    const newDataMap = new Map(newData.map(item => [item.id, item]))
    const historyMap = new Map(historyData.map(item => [item.id, item]))
    
    // 开始合并：新数据优先
    const mergedData: NewsItem[] = []
    const addedIds = new Set<string>()

    // 1. 首先添加所有新数据
    for (const item of newData) {
      if (mergedData.length >= maxItems) break
      mergedData.push(item)
      addedIds.add(item.id)
    }

    // 2. 然后添加历史数据中新数据没有的项目
    for (const historyItem of historyData) {
      if (mergedData.length >= maxItems) break
      
      if (!addedIds.has(historyItem.id)) {
        mergedData.push({
          id: historyItem.id,
          title: historyItem.title,
          url: historyItem.url,
          mobileUrl: historyItem.mobileUrl,
          pubDate: historyItem.pubDate,
          extra: historyItem.extra
        })
        addedIds.add(historyItem.id)
      }
    }

    return mergedData.slice(0, maxItems)
  }

  /**
   * 检查是否应该跳过抓取（避免与手动刷新冲突）
   * @param lastUpdateTime 上次更新时间
   * @param conflictWindowMs 冲突窗口时间（毫秒）
   * @returns 是否应该跳过
   */
  static shouldSkipFetch(lastUpdateTime: number, conflictWindowMs: number = 300000): boolean {
    const now = Date.now()
    return now - lastUpdateTime < conflictWindowMs
  }

  /**
   * 计算数据新鲜度分数
   * @param timestamp 时间戳
   * @param intervalMs 刷新间隔
   * @returns 新鲜度分数 (0-1, 1表示最新)
   */
  static calculateFreshnessScore(timestamp: number, intervalMs: number): number {
    const now = Date.now()
    const age = now - timestamp
    
    if (age <= 0) return 1
    if (age >= intervalMs) return 0
    
    return 1 - (age / intervalMs)
  }

  /**
   * 获取数据源的优先级
   * @param isManualRefresh 是否手动刷新
   * @param cacheAge 缓存年龄
   * @param historyAge 历史数据年龄
   * @param interval 刷新间隔
   * @returns 数据源优先级 ('fresh_history' | 'cache' | 'fetch_required')
   */
  static getDataSourcePriority(
    isManualRefresh: boolean,
    cacheAge: number,
    historyAge: number,
    interval: number
  ): 'fresh_history' | 'cache' | 'fetch_required' {
    // 手动刷新时，总是需要抓取新数据
    if (isManualRefresh) {
      return 'fetch_required'
    }

    // 历史数据很新，直接使用
    if (historyAge < interval) {
      return 'fresh_history'
    }

    // 缓存数据可用
    if (cacheAge < interval * 2) { // 缓存有效期是间隔的2倍
      return 'cache'
    }

    // 需要抓取新数据
    return 'fetch_required'
  }

  /**
   * 记录数据同步日志
   * @param sourceId 数据源ID
   * @param action 操作类型
   * @param details 详细信息
   */
  static logDataSync(
    sourceId: string, 
    action: 'merge' | 'skip' | 'fetch' | 'cache_hit' | 'history_hit',
    details: Record<string, any> = {}
  ) {
    const timestamp = new Date().toISOString()
    const logMessage = `[DataSync] ${timestamp} - ${sourceId}: ${action}`
    
    switch (action) {
      case 'merge':
        logger.info(`${logMessage} - merged ${details.newCount} new + ${details.historyCount} history = ${details.totalCount} items`)
        break
      case 'skip':
        logger.info(`${logMessage} - skipped due to recent update (${details.ageSeconds}s ago)`)
        break
      case 'fetch':
        logger.info(`${logMessage} - fetched ${details.count} items`)
        break
      case 'cache_hit':
        logger.info(`${logMessage} - served from cache (${details.ageSeconds}s old)`)
        break
      case 'history_hit':
        logger.info(`${logMessage} - served from history (${details.ageSeconds}s old)`)
        break
      default:
        logger.info(logMessage)
    }
  }
}

/**
 * 数据同步状态管理
 */
export class SyncStateManager {
  private static lastFetchTimes = new Map<string, number>()
  private static conflictWindow = 300000 // 5分钟冲突窗口

  /**
   * 记录抓取时间
   */
  static recordFetchTime(sourceId: string, timestamp: number = Date.now()) {
    this.lastFetchTimes.set(sourceId, timestamp)
  }

  /**
   * 获取上次抓取时间
   */
  static getLastFetchTime(sourceId: string): number {
    return this.lastFetchTimes.get(sourceId) || 0
  }

  /**
   * 检查是否在冲突窗口内
   */
  static isInConflictWindow(sourceId: string): boolean {
    const lastFetch = this.getLastFetchTime(sourceId)
    return DataSyncManager.shouldSkipFetch(lastFetch, this.conflictWindow)
  }

  /**
   * 清理过期的状态记录
   */
  static cleanup() {
    const now = Date.now()
    const expireTime = this.conflictWindow * 2 // 保留2倍冲突窗口时间的记录
    
    for (const [sourceId, timestamp] of this.lastFetchTimes.entries()) {
      if (now - timestamp > expireTime) {
        this.lastFetchTimes.delete(sourceId)
      }
    }
  }
}

// 定期清理状态记录
setInterval(() => {
  SyncStateManager.cleanup()
}, 600000) // 每10分钟清理一次
