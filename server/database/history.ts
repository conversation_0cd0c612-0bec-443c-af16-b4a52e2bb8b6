import process from "node:process"
import type { NewsItem } from "@shared/types"
import type { Database } from "db0"

// 改进的数据库类型检测函数
function isMySQLDatabase(): boolean {
  const hasMySQLConfig = process.env.MYSQL_HOST && 
                        process.env.MYSQL_USER && 
                        process.env.MYSQL_PASSWORD && 
                        process.env.MYSQL_DATABASE
  
  if (hasMySQLConfig) {
    console.log('🔍 检测到 MySQL 配置，使用 MySQL 数据库')
    return true
  } else {
    console.log('🔍 未检测到 MySQL 配置，使用 SQLite 数据库')
    return false
  }
}

export interface HistoryItem extends NewsItem {
  source_id: string
  created_at: number
  updated_at: number
}

export class HistoryTable {
  private db: Database
  private isMySQL: boolean
  
  constructor(db: Database) {
    this.db = db
    this.isMySQL = isMySQLDatabase()
  }

  async init() {
    if (this.isMySQL) {
      // MySQL syntax
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS news_history (
          id VARCHAR(255) NOT NULL,
          source_id VARCHAR(255) NOT NULL,
          title TEXT NOT NULL,
          url TEXT,
          mobile_url TEXT,
          pub_date BIGINT,
          extra TEXT,
          created_at BIGINT NOT NULL,
          updated_at BIGINT NOT NULL,
          PRIMARY KEY (id, source_id),
          INDEX idx_source_created (source_id, created_at),
          INDEX idx_created_at (created_at)
        );
      `).run()
    } else {
      // SQLite syntax
      await this.db.prepare(`
        CREATE TABLE IF NOT EXISTS news_history (
          id TEXT NOT NULL,
          source_id TEXT NOT NULL,
          title TEXT NOT NULL,
          url TEXT,
          mobile_url TEXT,
          pub_date INTEGER,
          extra TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          PRIMARY KEY (id, source_id)
        );
      `).run()
      
      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_source_created ON news_history(source_id, created_at);
      `).run()
      
      await this.db.prepare(`
        CREATE INDEX IF NOT EXISTS idx_created_at ON news_history(created_at);
      `).run()
    }
    logger.success(`init news_history table`)
  }

  async saveNews(sourceId: string, newsItems: NewsItem[]) {
    const now = Date.now()
    
    for (const item of newsItems) {
      const extraJson = item.extra ? JSON.stringify(item.extra) : null
      
      if (this.isMySQL) {
        // MySQL syntax - use ON DUPLICATE KEY UPDATE
        await this.db.prepare(`
          INSERT INTO news_history (id, source_id, title, url, mobile_url, pub_date, extra, created_at, updated_at) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) 
          ON DUPLICATE KEY UPDATE 
            title = VALUES(title),
            url = VALUES(url),
            mobile_url = VALUES(mobile_url),
            pub_date = VALUES(pub_date),
            extra = VALUES(extra),
            updated_at = VALUES(updated_at)
        `).run(
          item.id,
          sourceId,
          item.title,
          item.url || null,
          item.mobileUrl || null,
          item.pubDate || null,
          extraJson,
          now,
          now
        )
      } else {
        // SQLite syntax - use INSERT OR REPLACE
        await this.db.prepare(`
          INSERT OR REPLACE INTO news_history (id, source_id, title, url, mobile_url, pub_date, extra, created_at, updated_at) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          item.id,
          sourceId,
          item.title,
          item.url || null,
          item.mobileUrl || null,
          item.pubDate || null,
          extraJson,
          now,
          now
        )
      }
    }
    
    logger.success(`saved ${newsItems.length} news items for source ${sourceId}`)
  }

  async getNewsBySource(sourceId: string, limit: number = 30, offset: number = 0): Promise<HistoryItem[]> {
    const rows = await this.db.prepare(`
      SELECT * FROM news_history 
      WHERE source_id = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `).all(sourceId, limit, offset) as any[]
    
    return rows.map(row => ({
      id: row.id,
      source_id: row.source_id,
      title: row.title,
      url: row.url,
      mobileUrl: row.mobile_url,
      pubDate: row.pub_date,
      extra: row.extra ? JSON.parse(row.extra) : undefined,
      created_at: row.created_at,
      updated_at: row.updated_at
    }))
  }

  async getLatestNews(limit: number = 100): Promise<HistoryItem[]> {
    const rows = await this.db.prepare(`
      SELECT * FROM news_history 
      ORDER BY created_at DESC 
      LIMIT ?
    `).all(limit) as any[]
    
    return rows.map(row => ({
      id: row.id,
      source_id: row.source_id,
      title: row.title,
      url: row.url,
      mobileUrl: row.mobile_url,
      pubDate: row.pub_date,
      extra: row.extra ? JSON.parse(row.extra) : undefined,
      created_at: row.created_at,
      updated_at: row.updated_at
    }))
  }

  async cleanupOldNews(retentionDays: number = 30) {
    const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000)
    
    const result = await this.db.prepare(`
      DELETE FROM news_history 
      WHERE created_at < ?
    `).run(cutoffTime)
    
    logger.success(`cleaned up ${result.changes} old news items older than ${retentionDays} days`)
    return result.changes
  }

  async getSourceStats(): Promise<Array<{source_id: string, count: number, latest: number}>> {
    const rows = await this.db.prepare(`
      SELECT 
        source_id,
        COUNT(*) as count,
        MAX(created_at) as latest
      FROM news_history 
      GROUP BY source_id
      ORDER BY latest DESC
    `).all() as any[]
    
    return rows
  }
}

export async function getHistoryTable() {
  try {
    const db = useDatabase()
    const historyTable = new HistoryTable(db)
    if (process.env.INIT_TABLE !== "false") await historyTable.init()
    return historyTable
  } catch (e) {
    logger.error("failed to init history table ", e)
  }
}
