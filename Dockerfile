FROM node:20.12.2-alpine AS builder
WORKDIR /usr/src
COPY . .
RUN corepack enable
RUN pnpm install
RUN pnpm run build

FROM node:20.12.2-alpine

# 安装必要的系统依赖
RUN apk add --no-cache \
    sqlite \
    curl \
    && rm -rf /var/cache/apk/*

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /usr/app

# 复制构建产物和脚本
COPY --from=builder /usr/src/dist/output ./output
COPY --from=builder /usr/src/package.json ./package.json
COPY --from=builder /usr/src/scripts/docker-entrypoint.sh ./docker-entrypoint.sh

# 创建数据目录并设置权限
RUN mkdir -p .data && \
    chmod +x ./docker-entrypoint.sh && \
    chown -R nextjs:nodejs /usr/app && \
    chmod -R 755 /usr/app

# 设置环境变量
ENV HOST=0.0.0.0 \
    PORT=4444 \
    NODE_ENV=production \
    INIT_TABLE=true \
    ENABLE_CACHE=true

# 切换到非root用户
USER nextjs

EXPOSE $PORT

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/api/scheduler || exit 1

ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["node", "output/server/index.mjs"]
