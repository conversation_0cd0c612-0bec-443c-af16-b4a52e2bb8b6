version: '3.8'

services:
  newsnow:
    image: ghcr.io/ourongxing/newsnow:latest
    container_name: newsnow
    restart: unless-stopped
    ports:
      - '4444:4444'
    volumes:
      - newsnow_data:/usr/app/.data
    environment:
      - HOST=0.0.0.0
      - PORT=4444
      - NODE_ENV=production
      - DOCKER_ENV=true
      # Authentication (填入你的GitHub OAuth应用信息)
      - G_CLIENT_ID=your_github_client_id
      - G_CLIENT_SECRET=your_github_client_secret
      - JWT_SECRET=your_jwt_secret_key_change_this_in_production
      # Database Configuration
      - INIT_TABLE=true
      - ENABLE_CACHE=true
      # MySQL Database Configuration
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=newsnow
      - MYSQL_PASSWORD=newsnow_password_change_this
      - MYSQL_DATABASE=newsnow
      - MYSQL_SSL=false
      # Scheduler Configuration
      - SCHEDULER_INTERVAL=600000    # 10分钟抓取间隔
      - DATA_RETENTION_DAYS=30       # 保留30天数据
      - CLEANUP_INTERVAL=86400000    # 24小时清理一次
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4444/api/scheduler"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mysql:
    image: mysql:8.0
    container_name: newsnow_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password_change_this
      MYSQL_DATABASE: newsnow
      MYSQL_USER: newsnow
      MYSQL_PASSWORD: newsnow_password_change_this
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init-mysql.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password_change_this"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  newsnow_data:
    name: newsnow_data
  mysql_data:
    name: newsnow_mysql_data
