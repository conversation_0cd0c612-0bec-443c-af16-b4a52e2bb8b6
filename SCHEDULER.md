# NewsNow 后台调度器和历史数据管理

## 概述

NewsNow 现在支持后台自动抓取新闻数据并将其存储到数据库中，实现了以下功能：

- ✅ **后台定时抓取**: 自动定期从所有新闻源抓取数据
- ✅ **历史数据存储**: 将抓取的新闻保存到数据库，支持 SQLite 和 MySQL
- ✅ **智能缓存策略**: 优先从历史数据返回，减少实时抓取频率
- ✅ **自动数据清理**: 根据配置的保留时间自动删除过期数据
- ✅ **管理接口**: 提供 RESTful API 和命令行工具管理调度器

## 配置说明

### 环境变量

在 `.env.server` 文件中配置以下参数：

```env
# 调度器配置
SCHEDULER_INTERVAL=600000      # 抓取间隔（毫秒），默认10分钟
DATA_RETENTION_DAYS=30         # 数据保留天数，默认30天
CLEANUP_INTERVAL=86400000      # 清理间隔（毫秒），默认24小时

# 基础配置
ENABLE_CACHE=true              # 启用缓存
INIT_TABLE=true                # 初始化数据库表
```

### Docker 配置

项目提供了完整的 Docker 部署方案：

#### 配置文件
- `docker-compose.yml` - SQLite 版本 (默认)
- `docker-compose.mysql.yml` - MySQL 版本
- `docker-compose.local.yml` - 开发环境版本

#### 自动化特性
- ✅ 自动创建数据库文件/表结构
- ✅ 自动启动后台调度器
- ✅ 健康检查和自动重启
- ✅ 数据持久化存储
- ✅ 完整的日志记录

#### 环境变量配置
```env
# 基础配置
NODE_ENV=production
DOCKER_ENV=true
INIT_TABLE=true
ENABLE_CACHE=true

# 调度器配置
SCHEDULER_INTERVAL=600000    # 10分钟
DATA_RETENTION_DAYS=30       # 30天
CLEANUP_INTERVAL=86400000    # 24小时
```

## 使用方法

### 1. 启动服务

```bash
# 开发环境
pnpm dev

# 生产环境
pnpm build
pnpm start

# Docker
docker compose up
```

### 2. 管理调度器

使用提供的管理脚本：

```bash
# 查看调度器状态
npx tsx scripts/manage-scheduler.ts status

# 启动调度器
npx tsx scripts/manage-scheduler.ts start

# 停止调度器
npx tsx scripts/manage-scheduler.ts stop

# 查看完整仪表板
npx tsx scripts/manage-scheduler.ts dashboard

# 添加新闻源到调度器
npx tsx scripts/manage-scheduler.ts add hackernews

# 从调度器移除新闻源
npx tsx scripts/manage-scheduler.ts remove hackernews

# 手动清理历史数据（保留7天）
npx tsx scripts/manage-scheduler.ts cleanup 7
```

### 3. API 接口

#### 调度器管理
- `GET /api/scheduler` - 获取调度器状态
- `POST /api/scheduler` - 启动调度器
- `DELETE /api/scheduler` - 停止调度器

#### 新闻源管理
- `POST /api/scheduler/sources` - 添加新闻源
- `DELETE /api/scheduler/sources?sourceId=xxx` - 移除新闻源

#### 历史数据查询
- `GET /api/history` - 获取历史新闻数据
- `GET /api/history?sourceId=xxx` - 获取特定源的历史数据
- `GET /api/history/stats` - 获取历史数据统计
- `DELETE /api/history?retentionDays=30` - 手动清理历史数据

#### 管理面板
- `GET /api/admin/dashboard` - 获取完整的仪表板数据

## 工作原理

### 1. 数据流程

```
新闻源 → 调度器抓取 → 历史数据库 → 缓存 → API响应
```

### 2. 智能缓存策略

1. **数据源优先级**:
   - 新鲜历史数据（在刷新间隔内）
   - 有效缓存数据（在TTL内）
   - 实时抓取（必要时）

2. **冲突解决机制**:
   - **智能数据合并**：新数据优先，历史数据补充
   - **冲突窗口检测**：避免手动刷新和调度器重复抓取
   - **状态同步管理**：跟踪抓取时间，防止数据冲突
   - **数据一致性保证**：确保手动刷新不会丢失调度器数据

3. **数据保存策略**:
   - 每次抓取的数据同时保存到历史表和缓存表
   - 历史表用于长期存储和数据恢复
   - 缓存表用于快速响应和数据合并
   - 自动去重和数据完整性检查

### 3. 自动清理

- 定期清理超过保留期的历史数据
- 可配置清理间隔和保留天数
- 支持手动触发清理

## 数据库结构

### 历史数据表 (news_history)

```sql
CREATE TABLE news_history (
  id TEXT NOT NULL,              -- 新闻ID
  source_id TEXT NOT NULL,       -- 新闻源ID
  title TEXT NOT NULL,           -- 标题
  url TEXT,                      -- 链接
  mobile_url TEXT,               -- 移动端链接
  pub_date INTEGER,              -- 发布时间
  extra TEXT,                    -- 额外信息（JSON）
  created_at INTEGER NOT NULL,   -- 创建时间
  updated_at INTEGER NOT NULL,   -- 更新时间
  PRIMARY KEY (id, source_id)
);
```

## 监控和维护

### 查看状态

```bash
# 查看调度器状态
curl http://localhost:3000/api/scheduler

# 查看历史数据统计
curl http://localhost:3000/api/history/stats

# 查看完整仪表板
curl http://localhost:3000/api/admin/dashboard
```

### 日志监控

调度器会输出详细的日志信息，包括：
- 📅 调度器启动/停止
- 📰 新闻抓取成功/失败
- 🧹 数据清理结果
- ⚠️  错误和警告信息

## 故障排除

### 常见问题

1. **调度器无法启动**
   - 检查 `ENABLE_CACHE` 是否为 `true`
   - 确认数据库连接正常
   - 查看服务器日志

2. **数据未更新**
   - 检查调度器是否运行
   - 确认新闻源配置正确
   - 查看抓取日志

3. **数据库空间不足**
   - 调整 `DATA_RETENTION_DAYS` 减少保留天数
   - 手动执行数据清理
   - 考虑使用 MySQL 替代 SQLite

### 性能优化

1. **调整抓取间隔**: 根据需求调整 `SCHEDULER_INTERVAL`
2. **优化保留策略**: 合理设置 `DATA_RETENTION_DAYS`
3. **数据库优化**: 生产环境建议使用 MySQL
4. **监控资源**: 定期检查磁盘空间和内存使用

## 升级说明

从旧版本升级时：

1. 更新环境变量配置
2. 运行数据库初始化（`INIT_TABLE=true`）
3. 重启服务以应用新功能
4. 验证调度器正常工作

现在您的 NewsNow 项目已经具备了完整的后台数据管理能力！
