# NewsNow Docker 部署指南

## 🚀 快速开始

### 一键部署 (推荐)

```bash
# 克隆项目
git clone https://github.com/ourongxing/newsnow.git
cd newsnow

# 快速启动
./start.sh
```

这将自动创建配置文件并启动服务。

### 手动部署

#### 1. SQLite 版本 (推荐新手)

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 2. MySQL 版本 (生产环境推荐)

```bash
# 使用 MySQL 配置启动
docker-compose -f docker-compose.mysql.yml up -d

# 查看日志
docker-compose -f docker-compose.mysql.yml logs -f
```

## 📋 配置说明

### 环境变量

创建 `.env.docker` 文件进行配置：

```env
# GitHub OAuth 配置 (可选，用于用户认证)
G_CLIENT_ID=your_github_client_id
G_CLIENT_SECRET=your_github_client_secret
JWT_SECRET=your_jwt_secret_key_change_this

# 调度器配置
SCHEDULER_INTERVAL=600000    # 抓取间隔：10分钟 (毫秒)
DATA_RETENTION_DAYS=30       # 数据保留：30天
CLEANUP_INTERVAL=86400000    # 清理间隔：24小时 (毫秒)

# 端口配置
PORT=4444
```

### 主要配置项说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `SCHEDULER_INTERVAL` | 600000 | 新闻抓取间隔（毫秒） |
| `DATA_RETENTION_DAYS` | 30 | 历史数据保留天数 |
| `CLEANUP_INTERVAL` | 86400000 | 数据清理间隔（毫秒） |
| `INIT_TABLE` | true | 是否自动初始化数据库表 |
| `ENABLE_CACHE` | true | 是否启用缓存功能 |

## 🛠️ 管理工具

### 使用管理脚本

```bash
# 查看帮助
./scripts/docker-deploy.sh help

# 启动服务 (SQLite)
./scripts/docker-deploy.sh start

# 启动服务 (MySQL)
./scripts/docker-deploy.sh start-mysql

# 查看状态
./scripts/docker-deploy.sh status

# 查看日志
./scripts/docker-deploy.sh logs

# 备份数据
./scripts/docker-deploy.sh backup

# 恢复数据
./scripts/docker-deploy.sh restore backup_file.tar.gz

# 停止服务
./scripts/docker-deploy.sh stop

# 清理所有数据
./scripts/docker-deploy.sh clean
```

### 常用 Docker Compose 命令

```bash
# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up --build -d

# 查看资源使用情况
docker-compose top
```

## 📊 部署后验证

### 1. 检查服务状态

```bash
# 检查容器状态
docker-compose ps

# 检查健康状态
docker ps --filter "name=newsnow"
```

### 2. 测试 API 接口

```bash
# 测试调度器状态
curl http://localhost:4444/api/scheduler

# 测试历史数据
curl http://localhost:4444/api/history?limit=5

# 测试管理面板
curl http://localhost:4444/api/admin/dashboard
```

### 3. 访问 Web 界面

- 主界面: http://localhost:4444
- 管理面板: http://localhost:4444/api/admin/dashboard
- API 文档: http://localhost:4444/api/scheduler

## 🗄️ 数据持久化

### SQLite 版本
- 数据存储在 Docker volume `newsnow_data` 中
- 数据库文件位置: `/usr/app/.data/db.sqlite3`

### MySQL 版本
- 应用数据: Docker volume `newsnow_data`
- MySQL 数据: Docker volume `newsnow_mysql_data`

### 数据备份

```bash
# 自动备份
./scripts/docker-deploy.sh backup

# 手动备份 SQLite
docker run --rm -v newsnow_data:/data -v $(pwd):/backup alpine tar czf /backup/newsnow_backup.tar.gz -C /data .

# 手动恢复 SQLite
docker run --rm -v newsnow_data:/data -v $(pwd):/backup alpine tar xzf /backup/newsnow_backup.tar.gz -C /data
```

## 🔧 故障排除

### 常见问题

1. **容器无法启动**
   ```bash
   # 查看详细日志
   docker-compose logs newsnow
   
   # 检查端口占用
   netstat -tlnp | grep 4444
   ```

2. **数据库连接失败**
   ```bash
   # 检查 MySQL 容器状态
   docker-compose ps mysql
   
   # 查看 MySQL 日志
   docker-compose logs mysql
   ```

3. **调度器未启动**
   ```bash
   # 手动启动调度器
   curl -X POST http://localhost:4444/api/scheduler
   
   # 检查环境变量
   docker-compose exec newsnow env | grep SCHEDULER
   ```

4. **健康检查失败**
   ```bash
   # 检查健康状态
   docker inspect newsnow | grep Health -A 10
   
   # 手动执行健康检查
   docker-compose exec newsnow curl -f http://localhost:4444/api/scheduler
   ```

### 性能优化

1. **调整抓取间隔**
   ```env
   # 减少服务器负载
   SCHEDULER_INTERVAL=1800000  # 30分钟
   
   # 增加抓取频率
   SCHEDULER_INTERVAL=300000   # 5分钟
   ```

2. **优化数据保留**
   ```env
   # 减少存储空间使用
   DATA_RETENTION_DAYS=7       # 保留7天
   CLEANUP_INTERVAL=3600000    # 1小时清理一次
   ```

3. **使用 MySQL**
   - 生产环境推荐使用 MySQL
   - 更好的并发性能
   - 更强的数据一致性

## 🔄 更新升级

### 更新到最新版本

```bash
# 拉取最新镜像
docker-compose pull

# 重启服务
docker-compose up -d

# 清理旧镜像
docker image prune -f
```

### 版本回滚

```bash
# 使用特定版本
docker-compose down
docker run -d --name newsnow -p 4444:4444 ghcr.io/ourongxing/newsnow:v0.0.30

# 或修改 docker-compose.yml 中的镜像标签
image: ghcr.io/ourongxing/newsnow:v0.0.30
```

## 📈 监控和日志

### 日志管理

```bash
# 实时查看日志
docker-compose logs -f --tail 100

# 查看特定时间的日志
docker-compose logs --since "2024-01-01T00:00:00"

# 导出日志
docker-compose logs > newsnow.log
```

### 监控指标

```bash
# 查看资源使用
docker stats newsnow

# 查看磁盘使用
docker system df

# 查看数据卷大小
docker volume ls
```

## 🚀 生产环境建议

1. **使用 MySQL 数据库**
2. **配置合适的抓取间隔**
3. **定期备份数据**
4. **监控磁盘空间**
5. **设置日志轮转**
6. **使用反向代理 (Nginx)**
7. **配置 HTTPS**
8. **设置防火墙规则**

现在您的 NewsNow 项目已经具备了完整的 Docker 部署能力！🎉
