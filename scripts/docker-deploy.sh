#!/bin/bash

# NewsNow Docker 部署脚本
# 用于快速部署和管理 NewsNow 容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "NewsNow Docker 部署脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start           启动 NewsNow (SQLite)"
    echo "  start-mysql     启动 NewsNow (MySQL)"
    echo "  start-dev       启动开发环境"
    echo "  stop            停止所有服务"
    echo "  restart         重启服务"
    echo "  logs            查看日志"
    echo "  status          查看服务状态"
    echo "  build           构建镜像"
    echo "  clean           清理容器和镜像"
    echo "  backup          备份数据"
    echo "  restore [file]  恢复数据"
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动 SQLite 版本"
    echo "  $0 start-mysql             # 启动 MySQL 版本"
    echo "  $0 logs                    # 查看日志"
    echo "  $0 backup                  # 备份数据"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 获取 Docker Compose 命令
get_compose_cmd() {
    if docker compose version &> /dev/null; then
        echo "docker compose"
    else
        echo "docker-compose"
    fi
}

# 启动服务 (SQLite)
start_sqlite() {
    print_info "启动 NewsNow (SQLite 数据库)..."
    
    COMPOSE_CMD=$(get_compose_cmd)
    $COMPOSE_CMD up -d
    
    print_success "NewsNow 已启动!"
    print_info "访问地址: http://localhost:4444"
    print_info "管理面板: http://localhost:4444/api/admin/dashboard"
    print_info "查看日志: $0 logs"
}

# 启动服务 (MySQL)
start_mysql() {
    print_info "启动 NewsNow (MySQL 数据库)..."
    
    COMPOSE_CMD=$(get_compose_cmd)
    $COMPOSE_CMD -f docker-compose.mysql.yml up -d
    
    print_success "NewsNow (MySQL) 已启动!"
    print_info "访问地址: http://localhost:4444"
    print_info "MySQL 端口: 3306"
    print_info "管理面板: http://localhost:4444/api/admin/dashboard"
    print_info "查看日志: $0 logs"
}

# 启动开发环境
start_dev() {
    print_info "启动 NewsNow 开发环境..."
    
    COMPOSE_CMD=$(get_compose_cmd)
    $COMPOSE_CMD -f docker-compose.local.yml up -d
    
    print_success "NewsNow 开发环境已启动!"
    print_info "访问地址: http://localhost:4444"
    print_info "查看日志: $0 logs"
}

# 停止服务
stop_services() {
    print_info "停止 NewsNow 服务..."
    
    COMPOSE_CMD=$(get_compose_cmd)
    $COMPOSE_CMD down 2>/dev/null || true
    $COMPOSE_CMD -f docker-compose.mysql.yml down 2>/dev/null || true
    $COMPOSE_CMD -f docker-compose.local.yml down 2>/dev/null || true
    
    print_success "所有服务已停止"
}

# 重启服务
restart_services() {
    print_info "重启 NewsNow 服务..."
    stop_services
    sleep 2
    start_sqlite
}

# 查看日志
show_logs() {
    COMPOSE_CMD=$(get_compose_cmd)
    
    if docker ps | grep -q "newsnow"; then
        print_info "显示 NewsNow 日志 (按 Ctrl+C 退出)..."
        $COMPOSE_CMD logs -f newsnow 2>/dev/null || \
        $COMPOSE_CMD -f docker-compose.mysql.yml logs -f newsnow 2>/dev/null || \
        $COMPOSE_CMD -f docker-compose.local.yml logs -f newsnow 2>/dev/null || \
        docker logs -f newsnow
    else
        print_error "NewsNow 容器未运行"
        exit 1
    fi
}

# 查看状态
show_status() {
    print_info "NewsNow 服务状态:"
    echo ""
    
    if docker ps | grep -q "newsnow"; then
        docker ps --filter "name=newsnow" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        
        # 检查健康状态
        if docker ps --filter "name=newsnow" --filter "health=healthy" | grep -q "newsnow"; then
            print_success "服务运行正常"
        elif docker ps --filter "name=newsnow" --filter "health=unhealthy" | grep -q "newsnow"; then
            print_warning "服务运行异常"
        else
            print_info "健康检查进行中..."
        fi
        
        # 显示访问信息
        echo ""
        print_info "访问地址:"
        echo "  Web界面: http://localhost:4444"
        echo "  API文档: http://localhost:4444/api/scheduler"
        echo "  管理面板: http://localhost:4444/api/admin/dashboard"
    else
        print_warning "NewsNow 容器未运行"
        echo ""
        print_info "启动服务: $0 start"
    fi
}

# 构建镜像
build_image() {
    print_info "构建 NewsNow 镜像..."
    
    docker build -t newsnow:local .
    
    print_success "镜像构建完成"
}

# 清理容器和镜像
clean_all() {
    print_warning "这将删除所有 NewsNow 相关的容器、镜像和网络"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "清理容器和镜像..."
        
        # 停止并删除容器
        stop_services
        
        # 删除镜像
        docker rmi newsnow:local 2>/dev/null || true
        docker rmi ghcr.io/ourongxing/newsnow:latest 2>/dev/null || true
        
        # 清理未使用的资源
        docker system prune -f
        
        print_success "清理完成"
    else
        print_info "取消清理操作"
    fi
}

# 备份数据
backup_data() {
    print_info "备份 NewsNow 数据..."
    
    BACKUP_DIR="./backups"
    BACKUP_FILE="newsnow_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    mkdir -p "$BACKUP_DIR"
    
    if docker volume ls | grep -q "newsnow_data"; then
        docker run --rm -v newsnow_data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf "/backup/$BACKUP_FILE" -C /data .
        print_success "数据已备份到: $BACKUP_DIR/$BACKUP_FILE"
    else
        print_error "未找到数据卷 newsnow_data"
        exit 1
    fi
}

# 恢复数据
restore_data() {
    if [ -z "$1" ]; then
        print_error "请指定备份文件路径"
        echo "用法: $0 restore <backup_file>"
        exit 1
    fi
    
    BACKUP_FILE="$1"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        print_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    print_warning "这将覆盖现有数据"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "恢复数据从: $BACKUP_FILE"
        
        # 停止服务
        stop_services
        
        # 恢复数据
        docker run --rm -v newsnow_data:/data -v "$(pwd)":/backup alpine sh -c "cd /data && tar xzf /backup/$BACKUP_FILE"
        
        print_success "数据恢复完成"
        print_info "请重新启动服务: $0 start"
    else
        print_info "取消恢复操作"
    fi
}

# 主函数
main() {
    check_dependencies
    
    case "${1:-}" in
        start)
            start_sqlite
            ;;
        start-mysql)
            start_mysql
            ;;
        start-dev)
            start_dev
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        build)
            build_image
            ;;
        clean)
            clean_all
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$2"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: ${1:-}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
