#!/bin/sh

# Docker容器启动脚本
# 确保数据库初始化和调度器自动启动

set -e

echo "🐳 Starting NewsNow Docker Container..."

# 检查数据目录
if [ ! -d "/usr/app/.data" ]; then
    echo "📁 Creating data directory..."
    mkdir -p /usr/app/.data
fi

# 检查SQLite数据库文件
DB_FILE="/usr/app/.data/db.sqlite3"
if [ ! -f "$DB_FILE" ]; then
    echo "🗄️  Creating SQLite database file..."
    touch "$DB_FILE"
    echo "✅ Database file created: $DB_FILE"
else
    echo "✅ Database file exists: $DB_FILE"
fi

# 设置默认环境变量
export INIT_TABLE=${INIT_TABLE:-true}
export ENABLE_CACHE=${ENABLE_CACHE:-true}
export SCHEDULER_INTERVAL=${SCHEDULER_INTERVAL:-600000}
export DATA_RETENTION_DAYS=${DATA_RETENTION_DAYS:-30}
export CLEANUP_INTERVAL=${CLEANUP_INTERVAL:-86400000}

# 显示配置信息
echo "⚙️  Configuration:"
echo "   NODE_ENV: $NODE_ENV"
echo "   HOST: $HOST"
echo "   PORT: $PORT"
echo "   INIT_TABLE: $INIT_TABLE"
echo "   ENABLE_CACHE: $ENABLE_CACHE"
echo "   SCHEDULER_INTERVAL: ${SCHEDULER_INTERVAL}ms"
echo "   DATA_RETENTION_DAYS: ${DATA_RETENTION_DAYS} days"
echo "   CLEANUP_INTERVAL: ${CLEANUP_INTERVAL}ms"

# 检查数据库类型
if [ -n "$MYSQL_HOST" ] && [ -n "$MYSQL_USER" ] && [ -n "$MYSQL_PASSWORD" ] && [ -n "$MYSQL_DATABASE" ]; then
    echo "🗄️  Database: MySQL ($MYSQL_HOST:${MYSQL_PORT:-3306})"
    
    # 等待MySQL可用
    echo "⏳ Waiting for MySQL to be ready..."
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$MYSQL_HOST" "${MYSQL_PORT:-3306}" 2>/dev/null; then
            echo "✅ MySQL is ready!"
            break
        fi
        
        echo "   Attempt $attempt/$max_attempts: MySQL not ready, waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        echo "❌ MySQL connection timeout after $max_attempts attempts"
        exit 1
    fi
else
    echo "🗄️  Database: SQLite ($DB_FILE)"
fi

# 启动应用
echo "🚀 Starting NewsNow server..."
echo "📡 Server will be available at http://$HOST:$PORT"
echo "📊 Scheduler dashboard: http://$HOST:$PORT/api/admin/dashboard"
echo "📅 Scheduler API: http://$HOST:$PORT/api/scheduler"

# 执行主应用
exec "$@"
