#!/usr/bin/env tsx

/**
 * 测试冲突解决功能
 * 验证手动刷新和后台调度器之间的数据同步
 */

import { $fetch } from "ofetch"

const BASE_URL = process.env.BASE_URL || "http://localhost:4444"
const TEST_SOURCE = "hackernews" // 使用一个常见的新闻源进行测试

async function testConflictResolution() {
  console.log("🧪 Testing conflict resolution between manual refresh and scheduler...")
  
  try {
    // 1. 检查调度器状态
    console.log("\n📊 Checking scheduler status...")
    const schedulerStatus = await $fetch(`${BASE_URL}/api/scheduler`)
    console.log(`Scheduler running: ${schedulerStatus.data.isRunning}`)
    console.log(`Active schedulers: ${schedulerStatus.data.activeSchedulers.length}`)
    
    // 2. 获取初始数据（不强制刷新）
    console.log(`\n📰 Getting initial data for ${TEST_SOURCE}...`)
    const initialData = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}`)
    console.log(`Initial data: ${initialData.items.length} items, status: ${initialData.status}`)
    console.log(`Updated time: ${new Date(initialData.updatedTime).toISOString()}`)
    
    // 3. 等待一小段时间
    console.log("\n⏳ Waiting 3 seconds...")
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 4. 执行手动强制刷新
    console.log(`\n🔄 Performing manual refresh for ${TEST_SOURCE}...`)
    const manualRefreshData = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}&latest=true`)
    console.log(`Manual refresh: ${manualRefreshData.items.length} items, status: ${manualRefreshData.status}`)
    console.log(`Updated time: ${new Date(manualRefreshData.updatedTime).toISOString()}`)
    
    // 5. 立即再次获取数据（应该从缓存返回）
    console.log(`\n📋 Getting data immediately after manual refresh...`)
    const afterRefreshData = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}`)
    console.log(`After refresh: ${afterRefreshData.items.length} items, status: ${afterRefreshData.status}`)
    console.log(`Updated time: ${new Date(afterRefreshData.updatedTime).toISOString()}`)
    
    // 6. 检查历史数据
    console.log(`\n📚 Checking history data for ${TEST_SOURCE}...`)
    const historyData = await $fetch(`${BASE_URL}/api/history?sourceId=${TEST_SOURCE}&limit=5`)
    console.log(`History data: ${historyData.data.items.length} items`)
    if (historyData.data.items.length > 0) {
      const latest = historyData.data.items[0]
      console.log(`Latest history item: "${latest.title}" (${new Date(latest.created_at).toISOString()})`)
    }
    
    // 7. 等待调度器可能的抓取
    console.log("\n⏳ Waiting 10 seconds for potential scheduler fetch...")
    await new Promise(resolve => setTimeout(resolve, 10000))
    
    // 8. 再次检查数据
    console.log(`\n📊 Final data check for ${TEST_SOURCE}...`)
    const finalData = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}`)
    console.log(`Final data: ${finalData.items.length} items, status: ${finalData.status}`)
    console.log(`Updated time: ${new Date(finalData.updatedTime).toISOString()}`)
    
    // 9. 检查管理面板统计
    console.log("\n📈 Checking admin dashboard stats...")
    const dashboardData = await $fetch(`${BASE_URL}/api/admin/dashboard`)
    const sourceStats = dashboardData.data.history.sources.find((s: any) => s.sourceId === TEST_SOURCE)
    if (sourceStats) {
      console.log(`${TEST_SOURCE} stats: ${sourceStats.newsCount} items, last update: ${sourceStats.lastUpdateFormatted}`)
    }
    
    // 10. 分析结果
    console.log("\n🔍 Analysis:")
    
    // 检查数据一致性
    const dataConsistent = initialData.items.length <= finalData.items.length
    console.log(`✅ Data consistency: ${dataConsistent ? 'PASS' : 'FAIL'}`)
    
    // 检查时间戳合理性
    const timeProgression = initialData.updatedTime <= manualRefreshData.updatedTime
    console.log(`✅ Time progression: ${timeProgression ? 'PASS' : 'FAIL'}`)
    
    // 检查历史数据存在
    const historyExists = historyData.data.items.length > 0
    console.log(`✅ History data exists: ${historyExists ? 'PASS' : 'FAIL'}`)
    
    // 检查数据项目ID重复情况
    const finalIds = new Set(finalData.items.map((item: any) => item.id))
    const noDuplicates = finalIds.size === finalData.items.length
    console.log(`✅ No duplicate items: ${noDuplicates ? 'PASS' : 'FAIL'}`)
    
    console.log("\n🎉 Conflict resolution test completed!")
    
    if (dataConsistent && timeProgression && historyExists && noDuplicates) {
      console.log("✅ All tests PASSED - Conflict resolution is working correctly!")
    } else {
      console.log("❌ Some tests FAILED - Please check the implementation")
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error)
    process.exit(1)
  }
}

// 运行测试
testConflictResolution()
