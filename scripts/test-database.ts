#!/usr/bin/env tsx

import Database from "better-sqlite3"
import dotenv from "dotenv"
import { join } from "path"

// 加载环境变量
dotenv.config({ path: join(process.cwd(), ".env.server") })

async function testDatabase() {
  console.log("🧪 Testing database initialization...")

  try {
    // 创建SQLite数据库连接
    const db = new Database(".data/db.sqlite3")

    console.log("✅ Database connection established")

    // 测试创建缓存表
    console.log("📋 Creating cache table...")
    db.prepare(`
      CREATE TABLE IF NOT EXISTS cache (
        id TEXT PRIMARY KEY,
        updated INTEGER,
        data TEXT
      );
    `).run()
    console.log("✅ Cache table created")

    // 测试创建用户表
    console.log("📋 Creating user table...")
    db.prepare(`
      CREATE TABLE IF NOT EXISTS user (
        id TEXT PRIMARY KEY,
        email TEXT,
        data TEXT,
        type TEXT,
        created INTEGER,
        updated INTEGER
      );
    `).run()
    console.log("✅ User table created")

    // 测试创建历史数据表
    console.log("📋 Creating news_history table...")
    db.prepare(`
      CREATE TABLE IF NOT EXISTS news_history (
        id TEXT NOT NULL,
        source_id TEXT NOT NULL,
        title TEXT NOT NULL,
        url TEXT,
        mobile_url TEXT,
        pub_date INTEGER,
        extra TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        PRIMARY KEY (id, source_id)
      );
    `).run()

    db.prepare(`
      CREATE INDEX IF NOT EXISTS idx_source_created ON news_history(source_id, created_at);
    `).run()

    db.prepare(`
      CREATE INDEX IF NOT EXISTS idx_created_at ON news_history(created_at);
    `).run()
    console.log("✅ News history table created")

    // 测试插入一些示例数据
    console.log("📝 Inserting test data...")
    const now = Date.now()
    
    // 插入测试缓存数据
    db.prepare(`
      INSERT OR REPLACE INTO cache (id, data, updated) VALUES (?, ?, ?)
    `).run("test-source", JSON.stringify([
      {
        id: "test-1",
        title: "Test News 1",
        url: "https://example.com/1"
      },
      {
        id: "test-2",
        title: "Test News 2",
        url: "https://example.com/2"
      }
    ]), now)

    // 插入测试历史数据
    db.prepare(`
      INSERT OR REPLACE INTO news_history (id, source_id, title, url, mobile_url, pub_date, extra, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run("test-1", "test-source", "Test News 1", "https://example.com/1", null, now, null, now, now)

    db.prepare(`
      INSERT OR REPLACE INTO news_history (id, source_id, title, url, mobile_url, pub_date, extra, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run("test-2", "test-source", "Test News 2", "https://example.com/2", null, now, null, now, now)

    console.log("✅ Test data inserted")

    // 验证数据
    console.log("🔍 Verifying data...")

    const cacheData = db.prepare("SELECT * FROM cache WHERE id = ?").get("test-source")
    console.log("📊 Cache data:", cacheData ? "Found" : "Not found")

    const historyData = db.prepare("SELECT * FROM news_history WHERE source_id = ?").all("test-source")
    console.log("📊 History data:", historyData.length, "items found")

    // 显示表结构
    console.log("\n📋 Database schema:")
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all()
    for (const table of tables) {
      console.log(`  📄 Table: ${table.name}`)
      const columns = db.prepare(`PRAGMA table_info(${table.name})`).all()
      columns.forEach((col: any) => {
        console.log(`    - ${col.name}: ${col.type}${col.pk ? ' (PRIMARY KEY)' : ''}`)
      })
    }

    // 关闭数据库连接
    db.close()

    console.log("\n🎉 Database test completed successfully!")
    
  } catch (error) {
    console.error("❌ Database test failed:", error)
    process.exit(1)
  }
}

testDatabase()
