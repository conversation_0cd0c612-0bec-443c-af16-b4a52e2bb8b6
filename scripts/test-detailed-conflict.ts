#!/usr/bin/env tsx

/**
 * 详细的冲突解决测试
 * 模拟真实的用户操作场景
 */

import { $fetch } from "ofetch"

const BASE_URL = process.env.BASE_URL || "http://localhost:4445"
const TEST_SOURCE = "hackernews"

async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function testDetailedConflict() {
  console.log("🔬 Detailed conflict resolution test...")
  
  try {
    // 1. 确保调度器运行
    console.log("\n📊 Ensuring scheduler is running...")
    const schedulerStatus = await $fetch(`${BASE_URL}/api/scheduler`)
    if (!schedulerStatus.data.isRunning) {
      console.log("Starting scheduler...")
      await $fetch(`${BASE_URL}/api/scheduler`, { method: 'POST' })
      await sleep(2000)
    }
    console.log("✅ Scheduler is running")
    
    // 2. 获取基线数据
    console.log(`\n📊 Getting baseline data for ${TEST_SOURCE}...`)
    const baseline = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}`)
    console.log(`Baseline: ${baseline.items.length} items, updated: ${new Date(baseline.updatedTime).toISOString()}`)
    
    // 3. 模拟用户快速连续刷新（这是常见的冲突场景）
    console.log("\n🔄 Simulating rapid user refreshes...")
    
    const refresh1 = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}&latest=true`)
    console.log(`Refresh 1: ${refresh1.items.length} items, status: ${refresh1.status}`)
    
    await sleep(1000) // 1秒后再次刷新
    
    const refresh2 = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}&latest=true`)
    console.log(`Refresh 2: ${refresh2.items.length} items, status: ${refresh2.status}`)
    
    await sleep(1000) // 再1秒后第三次刷新
    
    const refresh3 = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}&latest=true`)
    console.log(`Refresh 3: ${refresh3.items.length} items, status: ${refresh3.status}`)
    
    // 4. 检查数据一致性
    console.log("\n🔍 Checking data consistency...")
    
    // 获取所有新闻项的ID
    const allIds = new Set()
    const addIds = (items: any[]) => items.forEach((item: any) => allIds.add(item.id))
    
    addIds(refresh1.items)
    addIds(refresh2.items)
    addIds(refresh3.items)
    
    console.log(`Total unique news IDs across refreshes: ${allIds.size}`)
    
    // 5. 检查历史数据完整性
    console.log("\n📚 Checking history data integrity...")
    const historyData = await $fetch(`${BASE_URL}/api/history?sourceId=${TEST_SOURCE}&limit=50`)
    console.log(`History contains: ${historyData.data.items.length} items`)
    
    // 检查历史数据中是否包含刷新的数据
    const historyIds = new Set(historyData.data.items.map((item: any) => item.id))
    const refreshIds = new Set(refresh3.items.map((item: any) => item.id))
    
    let matchCount = 0
    for (const id of refreshIds) {
      if (historyIds.has(id)) matchCount++
    }
    
    console.log(`History-refresh match: ${matchCount}/${refresh3.items.length} items`)
    
    // 6. 等待调度器可能的抓取并检查冲突避免
    console.log("\n⏳ Waiting for scheduler cycle and checking conflict avoidance...")
    await sleep(5000)
    
    const afterWait = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}`)
    console.log(`After wait: ${afterWait.items.length} items, updated: ${new Date(afterWait.updatedTime).toISOString()}`)
    
    // 7. 测试数据合并功能
    console.log("\n🔀 Testing data merging...")
    
    // 强制刷新获取最新数据
    const latestData = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}&latest=true`)
    console.log(`Latest data: ${latestData.items.length} items`)
    
    // 立即获取数据（应该是合并后的结果）
    const mergedData = await $fetch(`${BASE_URL}/api/s?id=${TEST_SOURCE}`)
    console.log(`Merged data: ${mergedData.items.length} items`)
    
    // 8. 分析结果
    console.log("\n📊 Test Results Analysis:")
    
    // 数据量一致性检查
    const dataVolumeConsistent = mergedData.items.length >= latestData.items.length
    console.log(`✅ Data volume consistency: ${dataVolumeConsistent ? 'PASS' : 'FAIL'}`)
    
    // 时间戳合理性检查
    const timestampReasonable = mergedData.updatedTime >= baseline.updatedTime
    console.log(`✅ Timestamp progression: ${timestampReasonable ? 'PASS' : 'FAIL'}`)
    
    // 历史数据完整性检查
    const historyComplete = historyData.data.items.length > 0
    console.log(`✅ History completeness: ${historyComplete ? 'PASS' : 'FAIL'}`)
    
    // 数据去重检查
    const finalIds = new Set(mergedData.items.map((item: any) => item.id))
    const noDuplicates = finalIds.size === mergedData.items.length
    console.log(`✅ No duplicates: ${noDuplicates ? 'PASS' : 'FAIL'}`)
    
    // 数据匹配度检查
    const goodMatch = matchCount / refresh3.items.length > 0.8 // 80%以上匹配
    console.log(`✅ History-refresh match rate: ${goodMatch ? 'PASS' : 'FAIL'} (${Math.round(matchCount / refresh3.items.length * 100)}%)`)
    
    // 9. 最终评估
    const allTestsPassed = dataVolumeConsistent && timestampReasonable && historyComplete && noDuplicates && goodMatch
    
    console.log("\n🎯 Final Assessment:")
    if (allTestsPassed) {
      console.log("🎉 ALL TESTS PASSED! Conflict resolution is working excellently!")
      console.log("✅ Manual refreshes and scheduler work harmoniously")
      console.log("✅ Data integrity is maintained across all operations")
      console.log("✅ No data loss or duplication detected")
    } else {
      console.log("⚠️  Some tests failed. Please review the implementation:")
      if (!dataVolumeConsistent) console.log("   - Data volume inconsistency detected")
      if (!timestampReasonable) console.log("   - Timestamp progression issue")
      if (!historyComplete) console.log("   - History data incomplete")
      if (!noDuplicates) console.log("   - Duplicate data detected")
      if (!goodMatch) console.log("   - Poor history-refresh data matching")
    }
    
  } catch (error) {
    console.error("❌ Detailed test failed:", error)
    process.exit(1)
  }
}

// 运行详细测试
testDetailedConflict()
