#!/usr/bin/env tsx

import { $fetch } from "ofetch"

const BASE_URL = process.env.BASE_URL || "http://localhost:3000"

async function main() {
  const command = process.argv[2]
  const sourceId = process.argv[3]

  try {
    switch (command) {
      case 'status':
        await getStatus()
        break
      case 'start':
        await startScheduler()
        break
      case 'stop':
        await stopScheduler()
        break
      case 'add':
        if (!sourceId) {
          console.error('Please provide a source ID')
          process.exit(1)
        }
        await addSource(sourceId)
        break
      case 'remove':
        if (!sourceId) {
          console.error('Please provide a source ID')
          process.exit(1)
        }
        await removeSource(sourceId)
        break
      case 'dashboard':
        await getDashboard()
        break
      case 'cleanup':
        const days = parseInt(sourceId) || 30
        await cleanupHistory(days)
        break
      default:
        showHelp()
    }
  } catch (error) {
    console.error('Error:', error)
    process.exit(1)
  }
}

async function getStatus() {
  const response = await $fetch(`${BASE_URL}/api/scheduler`)
  console.log('📅 Scheduler Status:')
  console.log(`  Running: ${response.data.isRunning}`)
  console.log(`  Active Schedulers: ${response.data.activeSchedulers.length}`)
  console.log(`  Sources: ${response.data.activeSchedulers.join(', ')}`)
  console.log(`  Config:`)
  console.log(`    Default Interval: ${response.data.config.defaultInterval}ms`)
  console.log(`    Retention Days: ${response.data.config.retentionDays}`)
  console.log(`    Cleanup Interval: ${response.data.config.cleanupInterval}ms`)
}

async function startScheduler() {
  const response = await $fetch(`${BASE_URL}/api/scheduler`, { method: 'POST' })
  console.log('✅', response.message)
}

async function stopScheduler() {
  const response = await $fetch(`${BASE_URL}/api/scheduler`, { method: 'DELETE' })
  console.log('✅', response.message)
}

async function addSource(sourceId: string) {
  const response = await $fetch(`${BASE_URL}/api/scheduler/sources`, {
    method: 'POST',
    body: { sourceId }
  })
  console.log('✅', response.message)
}

async function removeSource(sourceId: string) {
  const response = await $fetch(`${BASE_URL}/api/scheduler/sources?sourceId=${sourceId}`, {
    method: 'DELETE'
  })
  console.log('✅', response.message)
}

async function getDashboard() {
  const response = await $fetch(`${BASE_URL}/api/admin/dashboard`)
  const data = response.data
  
  console.log('📊 Dashboard Overview:')
  console.log(`\n🔧 System Info:`)
  console.log(`  Environment: ${data.system.nodeEnv}`)
  console.log(`  Database: ${data.system.databaseType}`)
  console.log(`  Cache Enabled: ${data.system.enableCache}`)
  
  console.log(`\n📅 Scheduler:`)
  console.log(`  Status: ${data.scheduler.isRunning ? '🟢 Running' : '🔴 Stopped'}`)
  console.log(`  Active Sources: ${data.scheduler.activeSchedulers.length}`)
  
  console.log(`\n📚 History:`)
  console.log(`  Total Sources: ${data.history.totalSources}`)
  console.log(`  Recent Sources:`)
  data.history.sources.slice(0, 5).forEach((source: any) => {
    console.log(`    ${source.sourceId}: ${source.newsCount} items (last: ${source.lastUpdateFormatted})`)
  })
  
  console.log(`\n📰 Available Sources:`)
  data.sources.forEach((source: any) => {
    const status = source.isActive ? '🟢' : '🔴'
    console.log(`    ${status} ${source.id} (interval: ${source.interval}ms)`)
  })
}

async function cleanupHistory(days: number) {
  const response = await $fetch(`${BASE_URL}/api/history?retentionDays=${days}`, {
    method: 'DELETE'
  })
  console.log('🧹', response.message)
}

function showHelp() {
  console.log(`
📅 NewsNow Scheduler Management Tool

Usage: tsx scripts/manage-scheduler.ts <command> [options]

Commands:
  status              Show scheduler status
  start               Start the scheduler
  stop                Stop the scheduler
  add <sourceId>      Add a source to scheduler
  remove <sourceId>   Remove a source from scheduler
  dashboard           Show full dashboard overview
  cleanup [days]      Cleanup old history data (default: 30 days)

Examples:
  tsx scripts/manage-scheduler.ts status
  tsx scripts/manage-scheduler.ts start
  tsx scripts/manage-scheduler.ts add hackernews
  tsx scripts/manage-scheduler.ts cleanup 7
  tsx scripts/manage-scheduler.ts dashboard

Environment Variables:
  BASE_URL            Server base URL (default: http://localhost:3000)
`)
}

main()
