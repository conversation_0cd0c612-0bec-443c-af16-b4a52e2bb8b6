#!/usr/bin/env tsx

import { describe, it, expect } from "vitest"

// 简单的功能测试脚本
async function testSchedulerFeatures() {
  console.log("🧪 Testing NewsNow Scheduler Features...")
  
  try {
    // 测试环境变量配置
    console.log("\n📋 Environment Configuration:")
    console.log(`  SCHEDULER_INTERVAL: ${process.env.SCHEDULER_INTERVAL || 'default (600000ms)'}`)
    console.log(`  DATA_RETENTION_DAYS: ${process.env.DATA_RETENTION_DAYS || 'default (30 days)'}`)
    console.log(`  CLEANUP_INTERVAL: ${process.env.CLEANUP_INTERVAL || 'default (86400000ms)'}`)
    console.log(`  ENABLE_CACHE: ${process.env.ENABLE_CACHE || 'default (true)'}`)
    console.log(`  NODE_ENV: ${process.env.NODE_ENV || 'development'}`)
    
    // 测试数据库类型检测
    const hasMySQLConfig = process.env.MYSQL_HOST && 
                          process.env.MYSQL_USER && 
                          process.env.MYSQL_PASSWORD && 
                          process.env.MYSQL_DATABASE
    
    console.log(`\n🗄️  Database Configuration:`)
    console.log(`  Type: ${hasMySQLConfig ? 'MySQL' : 'SQLite'}`)
    if (hasMySQLConfig) {
      console.log(`  Host: ${process.env.MYSQL_HOST}`)
      console.log(`  Database: ${process.env.MYSQL_DATABASE}`)
    }
    
    // 检查必要的文件是否存在
    console.log(`\n📁 File Structure Check:`)
    const fs = await import('fs')
    const path = await import('path')
    
    const filesToCheck = [
      'server/database/history.ts',
      'server/services/scheduler.ts',
      'server/api/scheduler/index.ts',
      'server/api/history/index.ts',
      'server/plugins/scheduler.ts',
      'scripts/manage-scheduler.ts'
    ]
    
    for (const file of filesToCheck) {
      const exists = fs.existsSync(file)
      console.log(`  ${exists ? '✅' : '❌'} ${file}`)
    }
    
    console.log(`\n✅ Basic tests completed successfully!`)
    console.log(`\n📝 Next Steps:`)
    console.log(`  1. Start the development server: pnpm dev`)
    console.log(`  2. Check scheduler status: tsx scripts/manage-scheduler.ts status`)
    console.log(`  3. View dashboard: tsx scripts/manage-scheduler.ts dashboard`)
    console.log(`  4. Test API endpoints at http://localhost:3000/api/scheduler`)
    
  } catch (error) {
    console.error("❌ Test failed:", error)
    process.exit(1)
  }
}

// 运行测试
testSchedulerFeatures()
