#!/usr/bin/env tsx

/**
 * 迁移脚本：将现有缓存数据迁移到历史数据表
 * 这个脚本会读取现有的缓存数据并将其保存到新的历史数据表中
 */

import process from "node:process"
import { createDatabase } from "db0"

async function migrateData() {
  console.log("🔄 Starting cache to history migration...")
  
  try {
    // 检测数据库类型
    const isMySQL = process.env.MYSQL_HOST && 
                   process.env.MYSQL_USER && 
                   process.env.MYSQL_PASSWORD && 
                   process.env.MYSQL_DATABASE

    let db
    if (isMySQL) {
      console.log("🔍 Using MySQL database")
      db = createDatabase({
        connector: "mysql2",
        options: {
          host: process.env.MYSQL_HOST,
          port: parseInt(process.env.MYSQL_PORT || "3306"),
          user: process.env.MYSQL_USER,
          password: process.env.MYSQL_PASSWORD,
          database: process.env.MYSQL_DATABASE,
          ssl: process.env.MYSQL_SSL === "true"
        }
      })
    } else {
      console.log("🔍 Using SQLite database")
      db = createDatabase({
        connector: "better-sqlite3",
        options: {
          name: ".data/db.sqlite3"
        }
      })
    }

    // 检查缓存表是否存在
    const cacheExists = await checkTableExists(db, "cache")
    if (!cacheExists) {
      console.log("❌ Cache table not found. Nothing to migrate.")
      return
    }

    // 创建历史数据表（如果不存在）
    await createHistoryTable(db, isMySQL)

    // 获取所有缓存数据
    const cacheData = await db.prepare("SELECT * FROM cache").all()
    console.log(`📊 Found ${cacheData.length} cache entries to migrate`)

    let migratedCount = 0
    let skippedCount = 0

    for (const cache of cacheData) {
      try {
        const sourceId = cache.id
        const newsItems = JSON.parse(cache.data)
        const cacheTime = cache.updated

        console.log(`📰 Migrating ${newsItems.length} items for source: ${sourceId}`)

        for (const item of newsItems) {
          const extraJson = item.extra ? JSON.stringify(item.extra) : null
          
          if (isMySQL) {
            await db.prepare(`
              INSERT INTO news_history (id, source_id, title, url, mobile_url, pub_date, extra, created_at, updated_at) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) 
              ON DUPLICATE KEY UPDATE 
                title = VALUES(title),
                url = VALUES(url),
                mobile_url = VALUES(mobile_url),
                pub_date = VALUES(pub_date),
                extra = VALUES(extra),
                updated_at = VALUES(updated_at)
            `).run(
              item.id,
              sourceId,
              item.title,
              item.url || null,
              item.mobileUrl || null,
              item.pubDate || null,
              extraJson,
              cacheTime, // 使用缓存时间作为创建时间
              cacheTime
            )
          } else {
            await db.prepare(`
              INSERT OR REPLACE INTO news_history (id, source_id, title, url, mobile_url, pub_date, extra, created_at, updated_at) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `).run(
              item.id,
              sourceId,
              item.title,
              item.url || null,
              item.mobileUrl || null,
              item.pubDate || null,
              extraJson,
              cacheTime,
              cacheTime
            )
          }
        }

        migratedCount += newsItems.length
        console.log(`✅ Migrated ${newsItems.length} items for ${sourceId}`)
      } catch (error) {
        console.error(`❌ Error migrating cache entry ${cache.id}:`, error)
        skippedCount++
      }
    }

    console.log(`\n🎉 Migration completed!`)
    console.log(`  ✅ Migrated: ${migratedCount} news items`)
    console.log(`  ⚠️  Skipped: ${skippedCount} cache entries`)
    console.log(`  📊 Total cache entries processed: ${cacheData.length}`)

    // 显示迁移后的统计信息
    const stats = await db.prepare(`
      SELECT 
        source_id,
        COUNT(*) as count,
        MAX(created_at) as latest
      FROM news_history 
      GROUP BY source_id
      ORDER BY latest DESC
    `).all()

    console.log(`\n📈 History table statistics:`)
    stats.forEach((stat: any) => {
      console.log(`  ${stat.source_id}: ${stat.count} items (latest: ${new Date(stat.latest).toISOString()})`)
    })

  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

async function checkTableExists(db: any, tableName: string): Promise<boolean> {
  try {
    await db.prepare(`SELECT 1 FROM ${tableName} LIMIT 1`).get()
    return true
  } catch {
    return false
  }
}

async function createHistoryTable(db: any, isMySQL: boolean) {
  if (isMySQL) {
    await db.prepare(`
      CREATE TABLE IF NOT EXISTS news_history (
        id VARCHAR(255) NOT NULL,
        source_id VARCHAR(255) NOT NULL,
        title TEXT NOT NULL,
        url TEXT,
        mobile_url TEXT,
        pub_date BIGINT,
        extra TEXT,
        created_at BIGINT NOT NULL,
        updated_at BIGINT NOT NULL,
        PRIMARY KEY (id, source_id),
        INDEX idx_source_created (source_id, created_at),
        INDEX idx_created_at (created_at)
      );
    `).run()
  } else {
    await db.prepare(`
      CREATE TABLE IF NOT EXISTS news_history (
        id TEXT NOT NULL,
        source_id TEXT NOT NULL,
        title TEXT NOT NULL,
        url TEXT,
        mobile_url TEXT,
        pub_date INTEGER,
        extra TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        PRIMARY KEY (id, source_id)
      );
    `).run()
    
    await db.prepare(`
      CREATE INDEX IF NOT EXISTS idx_source_created ON news_history(source_id, created_at);
    `).run()
    
    await db.prepare(`
      CREATE INDEX IF NOT EXISTS idx_created_at ON news_history(created_at);
    `).run()
  }
  
  console.log("✅ History table created/verified")
}

// 运行迁移
migrateData()
