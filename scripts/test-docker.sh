#!/bin/bash

# Docker 部署测试脚本

set -e

echo "🧪 Testing NewsNow Docker Deployment..."

# 清理之前的测试
echo "🧹 Cleaning up previous test containers..."
docker-compose down 2>/dev/null || true
docker-compose -f docker-compose.local.yml down 2>/dev/null || true

# 构建本地镜像
echo "🔨 Building local Docker image..."
docker build -t newsnow:test .

# 创建测试配置
echo "📝 Creating test configuration..."
cat > .env.test << EOF
G_CLIENT_ID=test_client_id
G_CLIENT_SECRET=test_client_secret
JWT_SECRET=test_jwt_secret_for_testing
SCHEDULER_INTERVAL=60000
DATA_RETENTION_DAYS=1
CLEANUP_INTERVAL=300000
EOF

# 启动测试容器
echo "🚀 Starting test container..."
docker run -d \
  --name newsnow_test \
  --env-file .env.test \
  -e NODE_ENV=production \
  -e DOCKER_ENV=true \
  -e INIT_TABLE=true \
  -e ENABLE_CACHE=true \
  -p 4445:4444 \
  -v newsnow_test_data:/usr/app/.data \
  newsnow:test

# 等待容器启动
echo "⏳ Waiting for container to start..."
sleep 10

# 检查容器状态
if ! docker ps | grep -q "newsnow_test"; then
    echo "❌ Container failed to start"
    docker logs newsnow_test
    exit 1
fi

echo "✅ Container started successfully"

# 测试健康检查
echo "🔍 Testing health check..."
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:4445/api/scheduler >/dev/null 2>&1; then
        echo "✅ Health check passed"
        break
    fi
    
    echo "   Attempt $attempt/$max_attempts: Service not ready, waiting..."
    sleep 5
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Health check failed"
    docker logs newsnow_test
    exit 1
fi

# 测试API端点
echo "🔍 Testing API endpoints..."

# 测试调度器状态
echo "   Testing scheduler status..."
if curl -s http://localhost:4445/api/scheduler | grep -q "success"; then
    echo "   ✅ Scheduler API working"
else
    echo "   ❌ Scheduler API failed"
    exit 1
fi

# 测试历史数据API
echo "   Testing history API..."
if curl -s http://localhost:4445/api/history | grep -q "success"; then
    echo "   ✅ History API working"
else
    echo "   ❌ History API failed"
    exit 1
fi

# 测试管理面板API
echo "   Testing admin dashboard..."
if curl -s http://localhost:4445/api/admin/dashboard | grep -q "success"; then
    echo "   ✅ Admin dashboard working"
else
    echo "   ❌ Admin dashboard failed"
    exit 1
fi

# 等待调度器启动并抓取数据
echo "⏳ Waiting for scheduler to fetch some data..."
sleep 30

# 检查是否有数据被抓取
echo "🔍 Checking if data was fetched..."
response=$(curl -s http://localhost:4445/api/admin/dashboard)
if echo "$response" | grep -q '"totalSources":[1-9]'; then
    echo "✅ Data fetching working"
else
    echo "⚠️  No data fetched yet (this might be normal)"
fi

# 显示容器日志的最后几行
echo "📋 Recent container logs:"
docker logs --tail 20 newsnow_test

# 清理测试环境
echo "🧹 Cleaning up test environment..."
docker stop newsnow_test
docker rm newsnow_test
docker volume rm newsnow_test_data 2>/dev/null || true
docker rmi newsnow:test 2>/dev/null || true
rm -f .env.test

echo ""
echo "🎉 Docker deployment test completed successfully!"
echo ""
echo "📝 Test Results:"
echo "   ✅ Container builds and starts correctly"
echo "   ✅ Health checks pass"
echo "   ✅ All API endpoints respond"
echo "   ✅ Database initialization works"
echo "   ✅ Scheduler configuration works"
echo ""
echo "🚀 Ready for production deployment!"
