{"name": "newsnow", "type": "module", "version": "0.0.31", "private": true, "packageManager": "pnpm@10.5.2", "author": {"url": "https://github.com/ourongxing/", "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, "homepage": "https://github.com/ourongxing/newsnow", "scripts": {"dev": "npm run presource && vite dev", "build": "npm run presource && vite build", "lint": "eslint", "presource": "tsx ./scripts/favicon.ts && tsx ./scripts/source.ts", "start": "node --env-file .env.server dist/output/server/index.mjs", "preview": "cross-env CF_PAGES=1 npm run build && wrangler pages dev dist/output/public", "deploy": "cross-env CF_PAGES=1 npm run build && wrangler pages deploy dist/output/public", "typecheck": "tsc --noEmit -p tsconfig.node.json && tsc --noEmit -p tsconfig.app.json", "release": "bumpp", "prepare": "simple-git-hooks", "log": "wrangler pages deployment tail --project-name newsnow", "test": "vitest -c vitest.config.ts", "scheduler:status": "tsx scripts/manage-scheduler.ts status", "scheduler:start": "tsx scripts/manage-scheduler.ts start", "scheduler:stop": "tsx scripts/manage-scheduler.ts stop", "scheduler:dashboard": "tsx scripts/manage-scheduler.ts dashboard", "migrate:cache": "tsx scripts/migrate-cache-to-history.ts", "test:scheduler": "tsx scripts/test-scheduler.ts"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.5.0", "@atlaskit/pragmatic-drag-and-drop-auto-scroll": "^2.1.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "@formkit/auto-animate": "^0.8.2", "@iconify-json/si": "^1.2.3", "@modelcontextprotocol/sdk": "^1.11.0", "@tanstack/react-query-devtools": "^5.66.11", "@tanstack/react-router": "^1.112.0", "@unocss/reset": "^66.0.0", "ahooks": "^3.8.4", "better-sqlite3": "^11.10.0", "cheerio": "^1.0.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "consola": "^3.4.0", "cookie-es": "^2.0.0", "dayjs": "1.11.13", "db0": "^0.3.1", "defu": "^6.1.4", "fast-xml-parser": "^5.0.8", "framer-motion": "^12.4.7", "h3": "^1.15.1", "iconv-lite": "^0.6.3", "jose": "^6.0.8", "jotai": "^2.12.1", "md5": "^2.3.0", "ofetch": "^1.4.1", "overlayscrollbars": "^2.11.1", "pnpm": "^10.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-use": "^17.6.0", "uncrypto": "^0.1.3", "zod": "^3.24.2"}, "devDependencies": {"@eslint-react/eslint-plugin": "^1.29.0", "@iconify-json/ph": "^1.2.2", "@napi-rs/pinyin": "^1.7.5", "@ourongxing/eslint-config": "3.2.3-beta.6", "@ourongxing/tsconfig": "^0.0.4", "@rollup/pluginutils": "^5.1.4", "@tanstack/react-query": "^5.66.11", "@tanstack/router-devtools": "^1.112.0", "@tanstack/router-plugin": "^1.112.0", "@types/md5": "^2.3.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@unocss/rule-utils": "^66.0.0", "@vitejs/plugin-react-swc": "^3.8.0", "bumpp": "^10.0.3", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "fast-glob": "^3.3.3", "favicons-scraper": "^1.3.2", "lint-staged": "^15.4.3", "mlly": "^1.7.4", "mockdate": "^3.0.5", "pnpm-patch-i": "^0.4.1", "rollup": "^4.34.8", "simple-git-hooks": "^2.11.1", "tsx": "^4.19.3", "typescript": "^5.8.2", "typescript-eslint": "^8.25.0", "unimport": "^4.1.2", "unocss": "^66.0.0", "vite": "^6.2.0", "vite-plugin-pwa": "^0.21.1", "vite-plugin-with-nitro": "0.0.3", "vitest": "^3.0.7", "workbox-build": "^7.3.0", "workbox-window": "^7.3.0", "wrangler": "4.14.1"}, "pnpm": {"patchedDependencies": {"dayjs": "patches/dayjs.patch"}, "onlyBuiltDependencies": ["@napi-rs/pinyin", "@parcel/watcher", "@swc/core", "esbuild", "better-sqlite3", "sharp", "simple-git-hooks", "unrs-resolver", "workerd"]}, "resolutions": {"cross-spawn": ">=7.0.6", "dayjs": "1.11.13", "nitropack": "npm:nitro-go@0.0.3", "picomatch": "^4.0.2", "react": "^19", "db0": "^0.3.1", "vite": "^6"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}