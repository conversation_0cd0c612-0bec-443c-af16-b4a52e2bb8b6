#!/bin/bash

# NewsNow 快速启动脚本

echo "🚀 NewsNow 快速启动"
echo "===================="

# 检查是否存在配置文件
if [ ! -f ".env.docker" ]; then
    echo "📝 创建 Docker 环境配置文件..."
    cat > .env.docker << EOF
# NewsNow Docker 环境配置
# 请根据需要修改以下配置

# GitHub OAuth 配置 (可选，用于用户登录)
G_CLIENT_ID=your_github_client_id
G_CLIENT_SECRET=your_github_client_secret
JWT_SECRET=your_jwt_secret_key_change_this_in_production

# 调度器配置
SCHEDULER_INTERVAL=600000    # 抓取间隔：10分钟 (毫秒)
DATA_RETENTION_DAYS=30       # 数据保留：30天
CLEANUP_INTERVAL=86400000    # 清理间隔：24小时 (毫秒)

# 端口配置
PORT=4444
EOF
    echo "✅ 配置文件已创建: .env.docker"
    echo "⚠️  请编辑 .env.docker 文件，填入你的 GitHub OAuth 信息"
    echo ""
fi

# 选择部署方式
echo "请选择部署方式:"
echo "1) SQLite 数据库 (推荐，简单易用)"
echo "2) MySQL 数据库 (适合生产环境)"
echo "3) 开发环境"
echo ""
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "🗄️  使用 SQLite 数据库启动..."
        docker-compose --env-file .env.docker up -d
        ;;
    2)
        echo "🗄️  使用 MySQL 数据库启动..."
        echo "⚠️  请确保已在 docker-compose.mysql.yml 中配置了 MySQL 密码"
        docker-compose -f docker-compose.mysql.yml --env-file .env.docker up -d
        ;;
    3)
        echo "🛠️  启动开发环境..."
        docker-compose -f docker-compose.local.yml --env-file .env.docker up -d
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 NewsNow 启动完成!"
echo ""
echo "📡 访问地址:"
echo "   Web界面: http://localhost:4444"
echo "   API文档: http://localhost:4444/api/scheduler"
echo "   管理面板: http://localhost:4444/api/admin/dashboard"
echo ""
echo "📋 常用命令:"
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "📚 更多管理命令请使用: ./scripts/docker-deploy.sh help"
