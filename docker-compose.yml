version: '3'

services:
  newsnow:
    build: .
    # image: ghcr.io/ourongxing/newsnow:latest
    container_name: newsnow
    restart: unless-stopped
    ports:
      - '4444:4444'
    volumes:
      - newsnow_data:/usr/app/.data
    environment:
      - HOST=0.0.0.0
      - PORT=4444
      - NODE_ENV=production
      - DOCKER_ENV=true
      # Authentication (填入你的GitHub OAuth应用信息)
      - G_CLIENT_ID=Ov23lixcg4QsAGpmx3zG
      - G_CLIENT_SECRET=283f70c39e4099b6339ccf9685d5a01f57e48ba8
      - JWT_SECRET=your_jwt_secret_key_change_this_in_production
      # Database Configuration
      - INIT_TABLE=true
      - ENABLE_CACHE=true
      # Scheduler Configuration (可根据需要调整)
      - SCHEDULER_INTERVAL=600000    # 10分钟抓取间隔
      - DATA_RETENTION_DAYS=30       # 保留30天数据
      - C<PERSON><PERSON>UP_INTERVAL=86400000    # 24小时清理一次
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4444/api/scheduler"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
      # MySQL Database Configuration (uncomment to use MySQL instead of SQLite)
      # - MYSQL_HOST=mysql
      # - MYSQL_PORT=3306
      # - MYSQL_USER=newsnow
      # - MYSQL_PASSWORD=your_password
      # - MYSQL_DATABASE=newsnow
      # - MYSQL_SSL=false
    # Uncomment the following lines to use MySQL database
    # depends_on:
    #   - mysql

  # Uncomment the following service to use MySQL database
  # mysql:
  #   image: mysql:8.0
  #   container_name: newsnow_mysql
  #   restart: always
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root_password
  #     MYSQL_DATABASE: newsnow
  #     MYSQL_USER: newsnow
  #     MYSQL_PASSWORD: your_password
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   command: --default-authentication-plugin=mysql_native_password

volumes:
  newsnow_data:
    name: newsnow_data
  # mysql_data:
  #   name: newsnow_mysql_data
