![](/public/og-image.png)

English | [简体中文](README.zh-CN.md) | [日本語](README.ja-JP.md)

Forked from https://github.com/ourongxing/newsnow  
Added background refresh and storage functionality by Augmentcode, see SCHEDULER.md for details.  

> [!NOTE]
> This is a demo version currently supporting Chinese only. A full-featured version with better customization and English content support will be released later.

**_Elegant reading of real-time and hottest news_**

## Features

- Clean and elegant UI design for optimal reading experience
- Real-time updates on trending news
- GitHub OAuth login with data synchronization
- 30-minute default cache duration (logged-in users can force refresh)
- Adaptive scraping interval (minimum 2 minutes) based on source update frequency to optimize resource usage and prevent IP bans
- support MCP server

```json
{
  "mcpServers": {
    "newsnow": {
      "command": "npx",
      "args": [
        "-y",
        "newsnow-mcp-server"
      ],
      "env": {
        "BASE_URL": "https://newsnow.busiyi.world"
      }
    }
  }
}
```
You can change the `BASE_URL` to your own domain.

## Deployment

### Basic Deployment

For deployments without login and caching:

1. Fork this repository
2. Import to platforms like Cloudflare Page or Vercel

### Cloudflare Page Configuration

- Build command: `pnpm run build`
- Output directory: `dist/output/public`

### GitHub OAuth Setup

1. [Create a GitHub App](https://github.com/settings/applications/new)
2. No special permissions required
3. Set callback URL to: `https://your-domain.com/api/oauth/github` (replace `your-domain` with your actual domain)
4. Obtain Client ID and Client Secret

### Environment Variables

Refer to `example.env.server`. For local development, rename it to `.env.server` and configure:

```env
# Github Client ID
G_CLIENT_ID=
# Github Client Secret
G_CLIENT_SECRET=
# JWT Secret, usually the same as Client Secret
JWT_SECRET=
# Initialize database, must be set to true on first run, can be turned off afterward
INIT_TABLE=true
# Whether to enable cache
ENABLE_CACHE=true

# Scheduler Configuration
# 定时抓取间隔（毫秒），默认10分钟 (600000ms)
SCHEDULER_INTERVAL=600000
# 数据保留天数，默认30天
DATA_RETENTION_DAYS=30
# 数据清理间隔（毫秒），默认24小时 (86400000ms)
CLEANUP_INTERVAL=86400000
```

### Database Support

Supported database connectors: https://db0.unjs.io/connectors
**Cloudflare D1 Database** is recommended.

1. Create D1 database in Cloudflare Worker dashboard
2. Configure database_id and database_name in wrangler.toml
3. If wrangler.toml doesn't exist, rename example.wrangler.toml and modify configurations
4. Changes will take effect on next deployment

### Docker Deployment

#### Quick Start (推荐)

```sh
# 克隆项目
git clone https://github.com/ourongxing/newsnow.git
cd newsnow

# 快速启动 (会自动创建配置文件)
./start.sh
```

#### 手动部署

**SQLite 版本 (推荐新手)**
```sh
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

**MySQL 版本 (生产环境推荐)**
```sh
# 编辑 MySQL 配置
cp docker-compose.mysql.yml docker-compose.yml
# 修改 MySQL 密码等配置

# 启动服务 (包含 MySQL)
docker-compose up -d

# 查看日志
docker-compose logs -f
```

#### 管理脚本

使用提供的管理脚本进行便捷操作：

```sh
# 查看帮助
./scripts/docker-deploy.sh help

# 启动服务 (SQLite)
./scripts/docker-deploy.sh start

# 启动服务 (MySQL)
./scripts/docker-deploy.sh start-mysql

# 查看状态
./scripts/docker-deploy.sh status

# 查看日志
./scripts/docker-deploy.sh logs

# 备份数据
./scripts/docker-deploy.sh backup

# 停止服务
./scripts/docker-deploy.sh stop
```

#### 配置说明

主要环境变量配置：

```env
# GitHub OAuth (可选，用于用户认证)
G_CLIENT_ID=your_github_client_id
G_CLIENT_SECRET=your_github_client_secret
JWT_SECRET=your_jwt_secret_key

# 调度器配置
SCHEDULER_INTERVAL=600000    # 抓取间隔 (毫秒)
DATA_RETENTION_DAYS=30       # 数据保留天数
CLEANUP_INTERVAL=86400000    # 清理间隔 (毫秒)
```

#### 部署后验证

1. 访问 http://localhost:4444 查看界面
2. 访问 http://localhost:4444/api/scheduler 查看调度器状态
3. 访问 http://localhost:4444/api/admin/dashboard 查看管理面板

#### 数据持久化

- SQLite: 数据存储在 Docker volume `newsnow_data` 中
- MySQL: 数据存储在 Docker volume `newsnow_mysql_data` 中
- 支持数据备份和恢复功能

### Background Scheduler & History Management

The project now includes a powerful background scheduler and history management system:

#### Features
- **Automatic Background Fetching**: Periodically fetches news from all sources in the background
- **Historical Data Storage**: Stores all fetched news in database with configurable retention
- **Smart Caching**: Prioritizes serving from history/cache to reduce real-time fetching
- **Automatic Cleanup**: Removes old data based on configurable retention period
- **Management APIs**: RESTful APIs for managing scheduler and viewing statistics

#### Configuration
```env
# Scheduler interval in milliseconds (default: 10 minutes)
SCHEDULER_INTERVAL=600000
# Data retention in days (default: 30 days)
DATA_RETENTION_DAYS=30
# Cleanup interval in milliseconds (default: 24 hours)
CLEANUP_INTERVAL=86400000
```

#### Management Commands
```sh
# Check scheduler status
tsx scripts/manage-scheduler.ts status

# Start/stop scheduler
tsx scripts/manage-scheduler.ts start
tsx scripts/manage-scheduler.ts stop

# Add/remove sources
tsx scripts/manage-scheduler.ts add hackernews
tsx scripts/manage-scheduler.ts remove hackernews

# View dashboard
tsx scripts/manage-scheduler.ts dashboard

# Cleanup old data
tsx scripts/manage-scheduler.ts cleanup 7
```

#### API Endpoints
- `GET /api/scheduler` - Get scheduler status
- `POST /api/scheduler` - Start scheduler
- `DELETE /api/scheduler` - Stop scheduler
- `GET /api/history` - Get historical news data
- `GET /api/history/stats` - Get history statistics
- `GET /api/admin/dashboard` - Get full dashboard data

## Development

> [!Note]
> Requires Node.js >= 20

```sh
corepack enable
pnpm i
pnpm dev
```

### Adding Data Sources

Refer to `shared/sources` and `server/sources` directories. The project provides complete type definitions and a clean architecture.

For detailed instructions on how to add new sources, see [CONTRIBUTING.md](CONTRIBUTING.md).

## Roadmap

- Add **multi-language support** (English, Chinese, more to come).
- Improve **personalization options** (category-based news, saved preferences).
- Expand **data sources** to cover global news in multiple languages.

**_release when ready_**
![](https://testmnbbs.oss-cn-zhangjiakou.aliyuncs.com/pic/20250328172146_rec_.gif?x-oss-process=base_webp)

## Contributing

Contributions are welcome! Feel free to submit pull requests or create issues for feature requests and bug reports.

See [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines on how to contribute, especially for adding new data sources.

## License

[MIT](./LICENSE) © ourongxing
