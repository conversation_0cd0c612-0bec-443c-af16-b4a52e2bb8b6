version: '3'

services:
  newsnow:
    build: .
    container_name: newsnow_dev
    restart: unless-stopped
    ports:
      - '4444:4444'
    volumes:
      - newsnow_data:/usr/app/.data
      # 开发环境可以挂载源码进行调试
      # - .:/usr/app/src:ro
    environment:
      - HOST=0.0.0.0
      - PORT=4444
      - NODE_ENV=production
      - DOCKER_ENV=true
      # Development Authentication (测试用)
      - G_CLIENT_ID=test_github_client_id
      - G_CLIENT_SECRET=test_github_client_secret
      - JWT_SECRET=test_jwt_secret_for_development_only
      # Database Configuration
      - INIT_TABLE=true
      - ENABLE_CACHE=true
      # Scheduler Configuration (开发环境使用较短间隔)
      - SCHEDULER_INTERVAL=300000     # 5分钟抓取间隔
      - DATA_RETENTION_DAYS=7         # 保留7天数据
      - CLEANUP_INTERVAL=3600000      # 1小时清理一次
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4444/api/scheduler"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
      # MySQL Database Configuration (uncomment to use MySQL instead of SQLite)
      # - MYSQL_HOST=mysql
      # - MYSQL_PORT=3306
      # - MYSQL_USER=newsnow
      # - MYSQL_PASSWORD=your_password
      # - MYSQL_DATABASE=newsnow
      # - MYSQL_SSL=false
    # Uncomment the following lines to use MySQL database
    # depends_on:
    #   - mysql

  # Uncomment the following service to use MySQL database
  # mysql:
  #   image: mysql:8.0
  #   container_name: newsnow_mysql
  #   restart: always
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root_password
  #     MYSQL_DATABASE: newsnow
  #     MYSQL_USER: newsnow
  #     MYSQL_PASSWORD: your_password
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   command: --default-authentication-plugin=mysql_native_password

volumes:
  newsnow_data:
    name: newsnow_data
  # mysql_data:
  #   name: newsnow_mysql_data
